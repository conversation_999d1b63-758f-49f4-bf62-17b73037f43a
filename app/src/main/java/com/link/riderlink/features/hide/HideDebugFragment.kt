package com.link.riderlink.features.hide

import com.link.riderlink.utils.ThemeManager
import android.content.Context
import android.graphics.Color
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.addCallback
import androidx.fragment.app.Fragment
import com.link.riderlink.R
import com.link.riderlink.RiderLink
import com.link.riderlink.databinding.FragmentHidedebugBinding
import com.link.riderlink.utils.LogcatHelper
import com.link.riderlink.utils.navigate
import com.link.riderlink.utils.popBackStack
import com.link.riderservice.api.RiderService
import com.link.riderservice.api.RiderServiceCallback
import com.link.riderservice.api.dto.NaviVersionRequest


class HideDebugFragment : Fragment() {
    private var _binding: FragmentHidedebugBinding? = null
    private val binding get() = _binding!!
    override fun onAttach(context: Context) {
        super.onAttach(context)
        requireActivity().onBackPressedDispatcher.addCallback(this) {
            popBackStack()
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentHidedebugBinding.inflate(inflater, container, false)
        binding.llBack.setOnClickListener{
            popBackStack()
        }
        val preference = context?.getSharedPreferences("config_auto_connection", Context.MODE_PRIVATE)
        val logcat = preference?.getBoolean("logcat_sw", false) == true
        val simulation = preference?.getBoolean("simulation_sw", false) == true
        binding.btnSwitchLoge.isChecked = logcat
        binding.btnSwitchNavi.isChecked = simulation
        RiderLink.instance.addConnectCallback(mCallback)
        RiderService.instance.sendMessageToRiderService(NaviVersionRequest())
        binding.itemApp.setOnClickListener{
            navigate(R.id.action_hidedebugFragment_to_appversionFragment)
        }
        binding.itemRiderlink.setOnClickListener{
            val bundle = Bundle()
            bundle.putString("param",riderlinkversion)
            navigate(R.id.action_hidedebugFragment_to_riderlinkversionFragment,bundle)
        }
        binding.itemAutolink.setOnClickListener{
            navigate(R.id.action_hidedebugFragment_to_autolinkversionFragment)
        }
        binding.itemProductkey.setOnClickListener{
           navigate(R.id.action_hidedebugFragment_to_productkeyFragment)
        }
        binding.itemUuid.setOnClickListener{
            navigate(R.id.action_hidedebugFragment_to_uuidFragment)
        }
        binding.itemCrash.setOnClickListener{
            val bundle = Bundle()
            bundle.putString("param","crash")
            navigate(R.id.action_hidedebugFragment_to_logcatFragment,bundle)
        }
        binding.itemLogcat.setOnClickListener{
            val bundle = Bundle()
            bundle.putString("param","logcat")
            navigate(R.id.action_hidedebugFragment_to_logcatFragment,bundle)
        }
        binding.btnSwitchLoge.setOnClickListener{
            if (preference != null) {
                with (preference.edit()) {
                    putBoolean("logcat_sw", binding.btnSwitchLoge.isChecked)
                    apply()
                }
            }
            if (binding.btnSwitchLoge.isChecked){
                startLogcatManager()
            }else{
                stopLogcatManager()
            }
        }
        binding.btnSwitchNavi.setOnClickListener{
            if (preference != null) {
                with (preference.edit()) {
                    putBoolean("simulation_sw", binding.btnSwitchNavi.isChecked)
                    apply()
                }
            }
            RiderLink.instance.setNaviType(binding.btnSwitchNavi.isChecked)
        }
        initTheme()
        ThemeManager.registerThemeChangeListener(themeCallback)
        return binding.root
    }

    private val mCallback = object : RiderServiceCallback(){
        override fun naviversionResponse(version: String) {
            super.naviversionResponse(version)
            riderlinkversion = version
            Log.e(TAG, "naviversionResponse: $riderlinkversion")
        }
    }

    private fun startLogcatManager() {
        activity?.let { LogcatHelper.getInstance(it.applicationContext).start() }
    }

    private fun stopLogcatManager(){
        activity?.let { LogcatHelper.getInstance(it.applicationContext).stop() }
    }

    fun initTheme(){
        binding.ibBack.setImageResource(ThemeManager.getCurrentThemeRes(requireContext(), R.drawable.set_back))
        binding.hideRoot.setBackgroundColor(Color.parseColor(ThemeManager.autoChangeStr("#FFFFFF","#2A3042")))
        binding.tvTitle.setTextColor(Color.parseColor(ThemeManager.autoChangeStr("#202229","#FFFFFF")))
        binding.tvApp.setTextColor(Color.parseColor(ThemeManager.autoChangeStr("#202229","#FFFFFF")))
        binding.tvRiderlink.setTextColor(Color.parseColor(ThemeManager.autoChangeStr("#202229","#FFFFFF")))
        binding.tvAutolink.setTextColor(Color.parseColor(ThemeManager.autoChangeStr("#202229","#FFFFFF")))
        binding.tvProductkey.setTextColor(Color.parseColor(ThemeManager.autoChangeStr("#202229","#FFFFFF")))
        binding.tvUuid.setTextColor(Color.parseColor(ThemeManager.autoChangeStr("#202229","#FFFFFF")))
        binding.tvCrash.setTextColor(Color.parseColor(ThemeManager.autoChangeStr("#202229","#FFFFFF")))
        binding.tvLogcat.setTextColor(Color.parseColor(ThemeManager.autoChangeStr("#202229","#FFFFFF")))
        binding.tvLogcatSw.setTextColor(Color.parseColor(ThemeManager.autoChangeStr("#202229","#FFFFFF")))
        binding.tvSimulationSw.setTextColor(Color.parseColor(ThemeManager.autoChangeStr("#202229","#FFFFFF")))
        binding.backgroundApp.setBackgroundResource(ThemeManager.getCurrentThemeRes(requireContext(), R.drawable.item_background_s))
        binding.backgroundRiderlink.setBackgroundResource(ThemeManager.getCurrentThemeRes(requireContext(), R.drawable.item_background_s))
        binding.backgroundAutolink.setBackgroundResource(ThemeManager.getCurrentThemeRes(requireContext(), R.drawable.item_background_s))
        binding.backgroundProductkey.setBackgroundResource(ThemeManager.getCurrentThemeRes(requireContext(), R.drawable.item_background_s))
        binding.backgroundUuid.setBackgroundResource(ThemeManager.getCurrentThemeRes(requireContext(), R.drawable.item_background_s))
        binding.backgroundCrash.setBackgroundResource(ThemeManager.getCurrentThemeRes(requireContext(), R.drawable.item_background_s))
        binding.backgroundLogcat.setBackgroundResource(ThemeManager.getCurrentThemeRes(requireContext(), R.drawable.item_background_s))
        binding.backgroundLogcatSw.setBackgroundResource(ThemeManager.getCurrentThemeRes(requireContext(), R.drawable.item_background_s))
        binding.backgroundSimulationSw.setBackgroundResource(ThemeManager.getCurrentThemeRes(requireContext(), R.drawable.item_background_s))
    }
    val themeCallback = object: ThemeManager.OnThemeChangeListener(){
        override fun onThemeChanged() {
            initTheme()
        }
    }
    override fun onDestroyView() {
        super.onDestroyView()
        ThemeManager.unregisterThemeChangeListener(themeCallback)
        _binding = null
    }

    companion object {
        private const val TAG = "HideDebugFragment"
        private var riderlinkversion = "请在连接平台后查看"
    }
}